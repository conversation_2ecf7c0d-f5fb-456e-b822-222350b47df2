# 死区配置指南

## 🎯 代码中的死区位置

main.py中有多个层次的死区设置，分别控制不同的功能：

### 1. **主要舵机控制死区** (第1248-1249行)

```python
servo_error_threshold = 18                 # 误差阈值，小于此值不进行舵机调整（加大死区，提高稳定性）
servo_stop_threshold = 9                  # 停止阈值，小于此值才停止舵机（加大死区，避免频繁启停）
```

**作用**：
- `servo_error_threshold = 18`: 启动死区，误差大于18像素才开始PID控制
- `servo_stop_threshold = 9`: 停止死区，误差小于9像素时停止舵机

**控制逻辑**：
- 误差 > 18px: 启动PID控制
- 误差 ≤ 9px: 停止舵机
- 9px < 误差 ≤ 18px: 滞后区域，轻微控制

### 2. **高级PID控制器死区** (第599-608行)

```python
# 动态死区 - 稳定时增大死区
current_dead_zone = 3  # 基础死区
if self.is_stable:
    current_dead_zone = current_dead_zone * 1.5  # 稳定时死区增大50%

# 死区判断：当误差很小时，舵机不动作
if abs(err_x) < current_dead_zone and abs(err_y) < current_dead_zone:
    if DEBUG:
        status = "稳定" if self.is_stable else "普通"
        print(f"💤 误差在死区内: X={err_x:+.1f}, Y={err_y:+.1f} (死区±{current_dead_zone:.1f}, {status})")
    return
```

**作用**：
- 基础死区：±3像素
- 稳定时死区：±4.5像素（增大50%）
- 防止微小误差导致的频繁调整

### 3. **高级PID控制器积分分离阈值** (第385, 395行)

```python
self.vertical_pid = AdvancedPIDController(
    Kp=self.vertical_pid_kp,
    Ki=self.vertical_pid_ki, 
    Kd=self.vertical_pid_kd,
    error_threshold=8,        # 积分分离阈值
    integral_limit=80,
    min_output=3,
    max_output=40
)
```

**作用**：
- `error_threshold=8`: 误差大于8时不进行积分，防止积分饱和
- `min_output=3`: 最小输出死区补偿，确保舵机能够运动

### 4. **水平舵机PWM死区** (第788-825行)

```python
def test_deadzone_4us(self):
    """测试4微秒死区范围"""
    # 测试死区边界值
    test_sequence = [
        (7.15, "停止位置 7.15%", 2),
        (7.17, "死区边界 7.17% (应该开始顺时针)", 3),
        (7.13, "死区边界 7.13% (应该开始逆时针)", 3),
        (7.16, "死区内 7.16% (应该停止)", 3),
        (7.14, "死区内 7.14% (应该停止)", 3),
    ]
```

**作用**：
- PWM死区：7.13% - 7.17% (±0.02%)
- 停止位置：7.15%
- 用于360度舵机的精确停止

## 🔧 死区参数调节

### 1. **主要死区调节** (推荐调节)

```python
# 位置：main.py 第1248-1249行

# 如果舵机太敏感，频繁微调
servo_error_threshold = 25    # 增大启动死区
servo_stop_threshold = 12     # 增大停止死区

# 如果舵机响应太慢
servo_error_threshold = 15    # 减小启动死区  
servo_stop_threshold = 6      # 减小停止死区

# 当前推荐值（已优化）
servo_error_threshold = 18    # 平衡值
servo_stop_threshold = 9      # 平衡值
```

### 2. **高级PID死区调节**

```python
# 位置：main.py 第600行

# 如果需要更精确的控制
current_dead_zone = 2         # 减小基础死区

# 如果需要更稳定的控制
current_dead_zone = 5         # 增大基础死区

# 当前设置
current_dead_zone = 3         # 平衡值
```

### 3. **积分分离阈值调节**

```python
# 位置：main.py 第385, 395行

# 如果出现积分饱和
error_threshold=12,           # 增大阈值，减少积分

# 如果稳态误差较大
error_threshold=5,            # 减小阈值，增加积分

# 当前设置
error_threshold=8,            # 平衡值
```

## 📊 死区效果分析

### 1. **死区层次结构**

```
误差大小 (像素)    控制行为
─────────────────────────────
> 18              全力PID控制
9 - 18            轻微控制 (30%强度)
3 - 9             高级PID死区判断
< 3               完全停止
```

### 2. **动态死区特性**

```python
# 普通状态
current_dead_zone = 3         # ±3像素死区

# 稳定状态  
current_dead_zone = 4.5       # ±4.5像素死区（增大50%）
```

### 3. **滞后控制特性**

```python
# 启动阈值：18px
# 停止阈值：9px
# 滞后区间：9-18px，使用30%控制强度
```

## 🎮 死区调节建议

### 1. **根据跟踪精度需求**

**高精度跟踪**：
```python
servo_error_threshold = 12    # 较小启动死区
servo_stop_threshold = 6      # 较小停止死区
current_dead_zone = 2         # 较小基础死区
```

**稳定跟踪**：
```python
servo_error_threshold = 25    # 较大启动死区
servo_stop_threshold = 12     # 较大停止死区  
current_dead_zone = 5         # 较大基础死区
```

**平衡设置**（当前）：
```python
servo_error_threshold = 18    # 平衡启动死区
servo_stop_threshold = 9      # 平衡停止死区
current_dead_zone = 3         # 平衡基础死区
```

### 2. **根据目标特性**

**快速移动目标**：
```python
servo_error_threshold = 15    # 减小死区，快速响应
servo_stop_threshold = 6
```

**静止目标**：
```python
servo_error_threshold = 22    # 增大死区，减少抖动
servo_stop_threshold = 11
```

### 3. **根据环境条件**

**噪声环境**：
```python
servo_error_threshold = 25    # 增大死区，抗干扰
current_dead_zone = 5
```

**清洁环境**：
```python
servo_error_threshold = 15    # 减小死区，高精度
current_dead_zone = 2
```

## 🔍 死区调试

### 1. **启用调试信息**

```python
DEBUG = True  # 查看死区判断过程
```

### 2. **观察调试输出**

```
💤 误差在死区内: X=+2.1, Y=-1.8 (死区±3.0, 普通)
💤 误差在死区内: X=+1.5, Y=-2.2 (死区±4.5, 稳定)
✅ 启动PID控制: 误差=19.2px > 启动阈值=18px
⏸ 目标精确居中，停止舵机: 误差=8.1px <= 停止阈值=9px
🔄 滞后区域，轻微控制: 误差=12.5px (停止阈值=9px < 误差 <= 启动阈值=18px)
```

### 3. **性能监控**

观察以下指标：
- 舵机启停频率
- 目标居中稳定性
- PID重置频率
- 系统响应速度

## 📋 总结

代码中的死区设置分为4个层次：

1. **主控制死区** (18px/9px) - 控制舵机启停
2. **高级PID死区** (3px/4.5px) - 防止微调
3. **积分分离阈值** (8px) - 防止积分饱和
4. **PWM死区** (±0.02%) - 舵机硬件特性

通过合理调节这些死区参数，可以在跟踪精度和系统稳定性之间找到最佳平衡点。
